# Getting Started with the Custom Framework

## Quick Start

### 1. Project Setup

Create a new project with this structure:

```
my-app/
├── framework/           # Framework files (copy these)
│   ├── app.js
│   ├── createDOMElement.js
│   └── router.js
├── app/                 # Your application
│   ├── components/      # UI components
│   ├── app.js          # Main app logic
│   └── index.html      # Entry point
└── README.md           # Documentation
```

### 2. Basic HTML Setup

Create `app/index.html`:

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My App</title>
</head>
<body>
    <div id="root"></div>
    <script type="module" src="app.js"></script>
</body>
</html>
```

### 3. Create Your First App

Create `app/app.js`:

```javascript
import { createReactiveState, setupRenderer, renderApp } from '../framework/app.js';

// Initialize state
const state = createReactiveState({
    message: 'Hello, Framework!',
    count: 0
}, renderApp);

// Event handlers
function handleClick() {
    state.count++;
    state.message = `Clicked ${state.count} times!`;
}

// Main component
function App() {
    return {
        type: 'div',
        props: { className: 'app' },
        children: [
            {
                type: 'h1',
                children: [state.message]
            },
            {
                type: 'button',
                props: {
                    'data-on': 'click:handleClick'
                },
                children: ['Click me!']
            }
        ]
    };
}

// Setup and render
setupRenderer('#root', App, {
    getEventContext: () => ({
        handleClick
    })
});

renderApp();
```

### 4. Run Your App

Since the framework uses ES modules, you need to serve the files through a web server:

**Option 1: Python (if installed)**
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```

**Option 2: Node.js (if installed)**
```bash
npx serve .
```

**Option 3: VS Code Live Server Extension**
- Install "Live Server" extension
- Right-click on `index.html` → "Open with Live Server"

Then open `http://localhost:8000/app/` in your browser.

## Next Steps

1. **Read the full documentation** in `README.md`
2. **Study the TodoMVC example** in the `app/` folder
3. **Try the simple counter example** in `example/simple-counter.html`
4. **Build your own components** following the patterns shown

## Framework Features Overview

### ✅ Virtual DOM
- Describe UI as JavaScript objects
- Efficient DOM updates
- Easy to debug and test

### ✅ Reactive State
- Automatic UI updates on state changes
- Simple property assignment
- Deep reactivity for objects and arrays

### ✅ Custom Events
- Declarative event binding with `data-on`
- Clean separation of concerns
- Easy to add/remove handlers

### ✅ Routing
- URL synchronization
- Browser back/forward support
- Programmatic navigation

### ✅ Components
- Reusable UI pieces
- Props-based data flow
- Composable architecture

## Common Patterns

### Creating Components
```javascript
function MyComponent({ title, items }) {
    return {
        type: 'div',
        props: { className: 'my-component' },
        children: [
            { type: 'h2', children: [title] },
            {
                type: 'ul',
                children: items.map(item => ({
                    type: 'li',
                    children: [item]
                }))
            }
        ]
    };
}
```

### Handling Forms
```javascript
function handleSubmit(event) {
    event.preventDefault();
    const formData = new FormData(event.target);
    const value = formData.get('inputName');
    // Process form data
}

// In your component:
{
    type: 'form',
    props: { 'data-on': 'submit:handleSubmit' },
    children: [
        {
            type: 'input',
            props: { name: 'inputName', type: 'text' }
        },
        {
            type: 'button',
            props: { type: 'submit' },
            children: ['Submit']
        }
    ]
}
```

### Conditional Rendering
```javascript
function App() {
    return {
        type: 'div',
        children: [
            // Conditional content
            ...(state.showMessage ? [{
                type: 'p',
                children: ['Message is visible!']
            }] : []),
            
            // Always visible
            {
                type: 'button',
                props: { 'data-on': 'click:toggleMessage' },
                children: ['Toggle Message']
            }
        ]
    };
}
```

Happy coding with your new framework! 🚀