# Custom JavaScript Framework

A complete, educational JavaScript framework built from scratch to demonstrate how modern frameworks work under the hood.

## 🎯 What You'll Learn

- How frameworks differ from libraries (Inversion of Control)
- Why Virtual DOM exists and how to build one
- How reactive state management works (using JavaScript Proxy)
- How to create custom event systems
- How client-side routing works
- How to organize code with components

## 📚 Documentation

**👉 [Read the Complete Framework Guide](FRAMEWORK_GUIDE.md)** - Beginner-friendly explanation of every concept

**📋 [Quick Start Guide](GETTING_STARTED.md)** - Get up and running quickly

**📊 [Implementation Summary](FRAMEWORK_SUMMARY.md)** - Technical overview of what was built

## 🚀 Quick Demo

```javascript
// Create reactive state
const state = createReactiveState({ count: 0 }, renderApp);

// Define UI with virtual DOM
function App() {
    return {
        type: 'div',
        children: [
            {
                type: 'h1',
                children: [`Count: ${state.count}`]
            },
            {
                type: 'button',
                props: { 'data-on': 'click:increment' },
                children: ['Click me!']
            }
        ]
    };
}

// Event handler
function increment() {
    state.count++; // UI automatically updates!
}

// Initialize framework
setupRenderer('#root', App, {
    getEventContext: () => ({ increment })
});
renderApp();
```

## 🏗️ Framework Features

### ✅ Virtual DOM
- Efficient DOM updates through JavaScript objects
- Declarative UI descriptions
- Automatic DOM synchronization

### ✅ Reactive State Management  
- Automatic UI updates when data changes
- Proxy-based reactivity system
- Single source of truth

### ✅ Custom Event Handling
- Declarative event binding with `data-on`
- Clean separation of concerns
- Framework-managed event lifecycle

### ✅ Client-Side Routing
- URL synchronization with app state
- Browser history support
- Single Page Application navigation

### ✅ Component System
- Reusable UI components
- Props-based data flow
- Composable architecture

## 🎮 Try the TodoMVC Demo

The framework includes a complete TodoMVC implementation demonstrating all features:

1. **Start a local server** (required for ES modules):
   ```bash
   python -m http.server 8000
   # or
   npx serve .
   ```

2. **Open** `http://localhost:8000/app/` in your browser

3. **Test all features**:
   - Add todos (Enter key)
   - Toggle completion (checkbox)
   - Delete todos (destroy button)
   - Filter todos (All/Active/Completed)
   - Clear completed todos
   - **Double-click to edit todos**
   - URL routing with browser back/forward

## 🏛️ Framework Architecture

```
Custom Framework
├── framework/
│   ├── app.js              # State management & rendering
│   ├── createDOMElement.js # Virtual DOM implementation
│   └── router.js           # Client-side routing
└── app/                    # TodoMVC example application
    ├── components/         # UI components
    ├── app.js             # Main app logic
    └── index.html         # Entry point
```

## 🎓 Educational Value

This framework was built to teach fundamental web development concepts:

- **No external dependencies** - Pure JavaScript implementation
- **Comprehensive documentation** - Every concept explained from first principles
- **Real-world example** - Complete TodoMVC application
- **Modern patterns** - Demonstrates current best practices

## 🔍 Framework vs Library

**Library**: You call library functions when you need them.
```javascript
const result = library.doSomething(data); // You control the flow
```

**Framework**: The framework calls your code when it decides to.
```javascript
framework.onStateChange(() => {
    // Framework calls this when state changes
});
```

Our framework demonstrates **Inversion of Control** - the framework manages the application lifecycle and calls your code at the right times.

## 🎯 Perfect For

- **Learning** how modern frameworks work internally
- **Understanding** Virtual DOM, reactivity, and component systems
- **Teaching** framework concepts to others
- **Building** simple applications without external dependencies
- **Interviews** - demonstrate deep understanding of framework principles

## 🚀 Next Steps

1. **Read** [FRAMEWORK_GUIDE.md](FRAMEWORK_GUIDE.md) for complete explanations
2. **Study** the TodoMVC implementation in `app/`
3. **Build** your own app using the framework
4. **Experiment** with extending the framework features

---

**Built with ❤️ for learning and understanding how frameworks really work!**