
import { createReactiveState, setupRenderer, renderApp } from '../framework/app.js';
import { Router } from '../framework/router.js';
import { TodoInput } from './components/TodoInput.js';
import { TodoList } from './components/TodoList.js';
import { TodoFooter } from './components/TodoFooter.js';

// --- Application State ---
const initialState = {
    todos: [
    ],
    filter: 'all',
    editingTodoId: null // ID of the todo currently being edited
};

// --- Initialize Framework ---
// Create the reactive state, passing the renderApp function to trigger re-renders
const state = createReactiveState(initialState, renderApp);

// Initialize the router
const router = new Router();

// --- Application Logic (Methods that modify state) ---

/**
 * Adds a new todo item to the state.
 * @param {string} text - The text of the new todo.
 */
function addTodo(text) {
    if (text.trim() === '') return;
    const newTodo = {
        id: Date.now(), // Simple ID generation
        text: text.trim(),
        completed: false
    };
    // Update state reactively - this will trigger renderApp()
    state.todos = [...state.todos, newTodo];
}

/**
 * Toggles the completion status of a todo item.
 * @param {number} id - The ID of the todo to toggle.
 */
function toggleTodo(id) {
    // Update state reactively
    state.todos = state.todos.map(todo =>
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
    );
}

/**
 * Deletes a todo item from the state.
 * @param {number} id - The ID of the todo to delete.
 */
function deleteTodo(id) {
    // Update state reactively
    state.todos = state.todos.filter(todo => todo.id !== id);
}

/**
 * Clears all completed todo items from the state.
 */
function clearCompletedTodos() {
    // Update state reactively
    state.todos = state.todos.filter(todo => !todo.completed);
}

/**
 * Sets the current filter in the state.
 * Also updates the browser URL using the router.
 * @param {string} filter - The filter to set ('all', 'active', 'completed').
 */
function setFilter(filter) {
    if (['all', 'active', 'completed'].includes(filter)) {
        state.filter = filter;
        // Update URL to reflect the new filter state using the router
        const path = filter === 'all' ? '/' : `/${filter}`;
        router.navigate(path);
    }
}

// --- Event Handlers for the UI (These will go into eventContext) ---
// These functions are referenced by name in the `data-on` attributes of VDOM elements.
// They often act as adapters, finding necessary data (like IDs) and calling core logic.

/**
 * Handles the 'keydown' event on the new todo input.
 * Adds a todo if the Enter key is pressed.
 * @param {Event} event - The keydown event.
 */
function handleAddTodo(event) {
    if (event.key === 'Enter') {
        const input = event.target;
        const text = input.value.trim();
        if (text) {
            addTodo(text);
            input.value = ''; // Clear the input field after adding
        }
    }
}

/**
 * Handles the 'change' event on a todo item's checkbox.
 * Finds the todo ID and calls toggleTodo.
 * @param {Event} event - The change event from the checkbox.
 */
function handleToggleTodo(event) {
    // Find the closest <li> ancestor that has the data-todo-id attribute
    const todoItemElement = event.target.closest('li[data-todo-id]');
    if (todoItemElement) {
        // Get the ID from the data attribute and convert it to a number
        const todoId = Number(todoItemElement.dataset.todoId);
        // Check if the conversion was successful
        if (!isNaN(todoId)) {
            toggleTodo(todoId); // Call the core logic function
        }
    }
}

/**
 * Handles the 'click' event on a todo item's delete button.
 * Finds the todo ID and calls deleteTodo.
 * @param {Event} event - The click event from the delete button.
 */
function handleDeleteTodo(event) {
    // Find the closest <li> ancestor that has the data-todo-id attribute
    const todoItemElement = event.target.closest('li[data-todo-id]');
    if (todoItemElement) {
        // Get the ID from the data attribute and convert it to a number
        const todoId = Number(todoItemElement.dataset.todoId);
        // Check if the conversion was successful
        if (!isNaN(todoId)) {
            deleteTodo(todoId); // Call the core logic function
        }
    }
}

/**
 * Handles the 'click' event on the "Clear completed" button.
 * Calls the clearCompletedTodos function.
 * @param {Event} event - The click event.
 */
function handleClearCompleted(event) {
    clearCompletedTodos();
}

/**
 * Handles the 'dblclick' event on a todo item's label.
 * Enters edit mode for the todo.
 * @param {Event} event - The double-click event.
 */
function handleEditTodo(event) {
    const todoItemElement = event.target.closest('li[data-todo-id]');
    if (todoItemElement) {
        const todoId = Number(todoItemElement.dataset.todoId);
        if (!isNaN(todoId)) {
            state.editingTodoId = todoId;
            // Focus the edit input after the next render cycle
            setTimeout(() => {
                const editInput = document.querySelector(`li[data-todo-id="${todoId}"] .edit`);
                if (editInput) {
                    editInput.focus();
                    editInput.select();
                }
            }, 10); // Small delay to ensure DOM is updated
        }
    }
}

/**
 * Handles saving the edited todo text.
 * @param {Event} event - The blur or keydown event.
 */
function handleSaveEdit(event) {
    if (state.editingTodoId === null) return;

    const newText = event.target.value.trim();
    if (newText === '') {
        // Delete todo if text is empty
        deleteTodo(state.editingTodoId);
    } else {
        // Update todo text
        state.todos = state.todos.map(todo =>
            todo.id === state.editingTodoId ? { ...todo, text: newText } : todo
        );
    }
    state.editingTodoId = null;
}

/**
 * Handles keydown events in edit mode.
 * @param {Event} event - The keydown event.
 */
function handleEditKeydown(event) {
    if (event.key === 'Enter') {
        handleSaveEdit(event);
    } else if (event.key === 'Escape') {
        state.editingTodoId = null; // Cancel editing
    }
}

// --- Main Render Function ---
// This function describes what the UI should look like based on the current state.
// It's the heart of the declarative approach.

/**
 * The main application render function.
 * It reads the current state and returns the virtual DOM for the entire app.
 * @returns {Object} The virtual DOM representation of the app.
 */
function App() {
    const activeCount = state.todos.filter(t => !t.completed).length;

    // --- Build the Virtual DOM ---
    return {
        type: 'div',
        props: {}, // No specific props for the root div
        children: [
            TodoInput(),
            // Conditionally render TodoList and TodoFooter if there are todos
            // Or render them always, letting components handle empty states if needed.
            // Rendering them conditionally based on todos.length is common.
            ...(state.todos.length > 0 ? [
                TodoList({
                    todos: state.todos,
                    filter: state.filter,
                    editingTodoId: state.editingTodoId
                }),
                TodoFooter({
                    activeCount: activeCount,
                    currentFilter: state.filter
                })
            ] : [])
            // Optionally add an info message or placeholder if todos are empty
            // and you want to show something before the first todo is added.
        ]
    };
}

// --- Setup Routes ---
// Configure the router with route handlers
router.addRoute('/', () => {
    state.filter = 'all';
});

router.addRoute('/active', () => {
    state.filter = 'active';
});

router.addRoute('/completed', () => {
    state.filter = 'completed';
});

// Set up route change callback to trigger re-renders
router.onRoute(() => {
    renderApp();
});


setupRenderer('#root', App, {
    getEventContext: () => ({
        handleAddTodo: handleAddTodo,
        handleToggleTodo: handleToggleTodo,
        handleDeleteTodo: handleDeleteTodo,
        handleClearCompleted: handleClearCompleted,
        handleEditTodo: handleEditTodo,
        handleSaveEdit: handleSaveEdit,
        handleEditKeydown: handleEditKeydown,
        setFilter: setFilter
    
    })
});


router.init();
