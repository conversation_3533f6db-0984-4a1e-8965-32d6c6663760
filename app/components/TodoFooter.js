// app/components/TodoFooter.js

/**
 * Creates a virtual DOM representation for the footer with counts and filters.
 * @param {Object} props - Properties for the TodoFooter.
 * @param {number} props.activeCount - Number of active todos.
 * @param {string} props.currentFilter - The currently active filter ('all', 'active', 'completed').
 * @returns {Object} Virtual DOM node for the TodoFooter.
 */
export function TodoFooter({ activeCount, currentFilter }) {
    const itemWord = activeCount === 1 ? 'item' : 'items';

    return {
        type: 'footer',
        props: { className: 'footer' },
        children: [
            {
                type: 'span',
                props: { className: 'todo-count' },
                children: [
                    { type: 'strong', children: [activeCount.toString()] },
                    ` ${itemWord} left`
                ]
            },
            {
                type: 'ul',
                props: { className: 'filters' },
                children: [
                    {
                        type: 'li',
                        children: [
                            {
                                type: 'a',
                                props: {
                                    href: '/',
                                    className: currentFilter === 'all' ? 'selected' : ''
                                },
                                children: ['All']
                            }
                        ]
                    },
                    {
                        type: 'li',
                        children: [
                            {
                                type: 'a',
                                props: {
                                    href: '/active',
                                    className: currentFilter === 'active' ? 'selected' : ''
                                },
                                children: ['Active']
                            }
                        ]
                    },
                    {
                        type: 'li',
                        children: [
                            {
                                type: 'a',
                                props: {
                                    href: '/completed',
                                    className: currentFilter === 'completed' ? 'selected' : ''
                                },
                                children: ['Completed']
                            }
                        ]
                    }
                ]
            },
            {
                type: 'button',
                props: {
                    className: 'clear-completed',
                    'data-on': 'click:handleClearCompleted'
                },
                children: ['Clear completed']
            }
        ]
    };
}
