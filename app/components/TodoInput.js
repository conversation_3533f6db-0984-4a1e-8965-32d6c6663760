// app/components/TodoInput.js

/**
 * Creates a virtual DOM representation for the new todo input.
 * Uses the data-on event handling pattern consistent with the rest of the app.
 * @returns {Object} Virtual DOM node for the TodoInput.
 */
export function TodoInput() {
    return {
        type: 'header',
        props: { className: 'header' },
        children: [
            {
                type: 'h1',
                children: ['todos']
            },
            {
                type: 'input',
                props: {
                    className: 'new-todo',
                    placeholder: 'What needs to be done?',
                    'data-on': 'keydown:handleAddTodo'
                }
            }
        ]
    };
}
