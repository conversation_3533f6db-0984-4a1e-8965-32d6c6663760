// app/components/TodoItem.js

/**
 * Creates a virtual DOM representation for a single Todo item.
 * Uses data-on event handling pattern consistent with the rest of the app.
 * @param {Object} props - Properties for the TodoItem.
 * @param {Object} props.todo - The todo object {id, text, completed}.
 * @param {boolean} props.isEditing - Whether this todo is currently being edited.
 * @returns {Object} Virtual DOM node for the TodoItem.
 */
export function TodoItem({ todo, isEditing = false }) {
    const className = [
        todo.completed ? 'completed' : '',
        isEditing ? 'editing' : ''
    ].filter(Boolean).join(' ');

    return {
        type: 'li',
        props: {
            className,
            'data-todo-id': todo.id // Store the todo ID for event handlers
        },
        children: [
            {
                type: 'div',
                props: { className: 'view' },
                children: [
                    {
                        type: 'input',
                        props: {
                            className: 'toggle',
                            type: 'checkbox',
                            checked: todo.completed,
                            'data-on': 'change:handleToggleTodo'
                        }
                    },
                    {
                        type: 'label',
                        props: {
                            'data-on': 'dblclick:handleEditTodo'
                        },
                        children: [todo.text]
                    },
                    {
                        type: 'button',
                        props: {
                            className: 'destroy',
                            'data-on': 'click:handleDeleteTodo'
                        }
                    }
                ]
            },
            // Editing input (shown when isEditing is true)
            ...(isEditing ? [{
                type: 'input',
                props: {
                    className: 'edit',
                    type: 'text',
                    value: todo.text,
                    'data-on': 'blur:handleSaveEdit keydown:handleEditKeydown'
                }
            }] : [])
        ]
    };
}
