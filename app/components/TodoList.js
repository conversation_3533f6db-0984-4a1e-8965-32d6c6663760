// app/components/TodoList.js
import { TodoItem } from './TodoItem.js';

/**
 * Creates a virtual DOM representation for the list of Todos.
 * @param {Object} props - Properties for the TodoList.
 * @param {Array} props.todos - The array of todo objects.
 * @param {string} props.filter - The current filter ('all', 'active', 'completed').
 * @param {number|null} props.editingTodoId - ID of the todo currently being edited.
 * @returns {Object} Virtual DOM node for the TodoList.
 */
export function TodoList({ todos, filter, editingTodoId }) {

    // Filter todos based on the current filter state
    const filteredTodos = todos.filter(todo => {
        if (filter === 'active') return !todo.completed;
        if (filter === 'completed') return todo.completed;
        return true; // 'all'
    });

    return {
        type: 'section',
        props: { className: 'main' },
        children: [
            {
                type: 'input',
                props: {
                    id: 'toggle-all',
                    className: 'toggle-all',
                    type: 'checkbox',
                    checked: todos.length > 0 && todos.every(t => t.completed)
                }
                // onchange would go here, passed from parent Todo<PERSON><PERSON>
            },
            {
                type: 'label',
                props: { htmlFor: 'toggle-all' },
                children: ['Mark all as complete']
            },
            {
                type: 'ul',
                props: { className: 'todo-list' },
                children: filteredTodos.map(todo =>
                    TodoItem({ 
                        todo,
                        isEditing: todo.id === editingTodoId
                    })
                )
            }
        ]
    };
}
