// framework/app.js
import { createDOMElement } from './createDOMElement.js';

// --- Reactive State Management ---
let currentRenderFunction = null;
let getEventContextFunction = () => ({});

/**
 * Creates a reactive state object using Proxy.
 * @param {Object} initialState - The initial state object.
 * @param {Function} renderFunction - The function to call on state change.
 * @returns {Proxy} The reactive state proxy.
 */
export function createReactiveState(initialState, renderFunction) {
    currentRenderFunction = renderFunction;

    function createStateProxy(target) {
        return new Proxy(target, {
            set(target, key, value, receiver) {
                // Handle nested objects/arrays for deep reactivity
                if (value && typeof value === 'object' && !Array.isArray(value)) {
                   target[key] = createStateProxy(value); // Recursively proxy new objects
                } else if (Array.isArray(value)) {
                    // Handle arrays: proxy objects within the array
                    const proxiedArray = value.map(item => {
                        if (item && typeof item === 'object' && !Array.isArray(item)) {
                            return createStateProxy(item); // Proxy nested objects in arrays
                        }
                        return item; // Primitives/arrays stay as is
                    });
                    target[key] = proxiedArray;
                } else {
                    target[key] = value; // Set primitives or non-object/array values directly
                }

                // Trigger the provided render function on any state change
                if (currentRenderFunction) {
                    currentRenderFunction();
                }
                return true; // Indicate successful property set
            }
        });
    }
    return createStateProxy(initialState);
}

// --- Rendering ---
let appRootContainer = null;
let appRenderFunction = null;

/**
 * Sets up the rendering environment.
 * @param {string} rootSelector - CSS selector for the root container.
 * @param {Function} renderFunction - Function that returns the virtual DOM (e.g., App()).
 * @param {Object} options - Additional options.
 * @param {Function} options.getEventContext - Function that returns the current event context object.
 */
export function setupRenderer(rootSelector, renderFunction, options = {}) {
    appRootContainer = document.querySelector(rootSelector);
    appRenderFunction = renderFunction;
    if (options.getEventContext && typeof options.getEventContext === 'function') {
        getEventContextFunction = options.getEventContext;
    }
    if (!appRootContainer) {
        console.error(`Framework setup error: Root container '${rootSelector}' not found in the DOM.`);
        // Depending on requirements, you might throw an error here
        // throw new Error(`Root container '${rootSelector}' not found.`);
    }
}

/**
 * Renders the application by generating the virtual DOM and updating the real DOM.
 * Fixes the recursion issue by clearing the container BEFORE creating new DOM.
 */
export function renderApp() {
    // Check if setup was successful
    if (!appRootContainer || !appRenderFunction) {
        console.error('Framework render error: Renderer not set up correctly. Call setupRenderer first.');
        return;
    }

    // 1. Generate the new Virtual DOM based on the current state
    // This function (e.g., App in app/app.js) should return a VDOM object
    const virtualDOM = appRenderFunction();

    // 2. Get the current event context for this render cycle
    // This provides the functions referenced in data-on attributes
    const currentEventContext = getEventContextFunction();

    // 3. CRITICAL: Clear the container FIRST
    // -------------------------------------
    // This step is essential to remove the old DOM elements and their attached event listeners
    // BEFORE creating and attaching the new DOM elements. This prevents listener duplication
    // which was causing the "too much recursion" error.
    // Setting innerHTML to an empty string effectively destroys the old subtree.
    appRootContainer.innerHTML = '';

    // 4. Create the new Real DOM from the Virtual DOM
    // Pass the event context so createDOMElement can bind handlers defined in data-on
    const realDOM = createDOMElement(virtualDOM, currentEventContext);

    // 5. Append the new DOM only if creation was successful
    if (realDOM) {
        // The container is now empty and ready for the new content
        appRootContainer.appendChild(realDOM);
    } else {
        // Handle case where VDOM didn't produce a valid DOM element
        // The container is already cleared, so it will just be empty.
        // This might be intentional (e.g., conditional rendering returning null)
        console.warn('Framework render warning: renderApp received null/undefined from renderFunction or createDOMElement failed.');
    }
}
