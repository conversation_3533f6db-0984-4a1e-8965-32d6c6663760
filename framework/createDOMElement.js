// framework/createDOMElement.js

/**
 * Converts a virtual DOM node into an actual DOM element.
 * @param {Object|string} node - The virtual DOM node (object) or text content (string).
 * @param {Object} eventContext - Object containing event handler functions.
 * @returns {Node} The corresponding real DOM node (Element or Text).
 */
export function createDOMElement(node, eventContext = {}) {
    // Handle text nodes
    if (typeof node === 'string') {
        return document.createTextNode(node);
    }

    // Handle element nodes
    if (typeof node === 'object' && node.type) {
        const element = document.createElement(node.type);

        // Set attributes/props
        if (node.props && typeof node.props === 'object') {
            for (const [key, value] of Object.entries(node.props)) {
                // Handle special cases
                if (key === 'className') {
                    element.className = value;
                } else if (key === 'data-on') {
                    // Handle data-on event binding: "event:handlerName" or multiple "event1:handler1 event2:handler2"
                    const eventBindings = value.split(' ');
                    eventBindings.forEach(binding => {
                        const [eventType, handlerName] = binding.split(':');
                        if (eventType && handlerName && eventContext[handlerName]) {
                            element.addEventListener(eventType, eventContext[handlerName]);
                        }
                    });
                    // Also set the data attribute for debugging/inspection
                    element.setAttribute(key, value);
                } else if (key.startsWith('on')) {
                    // Handle direct event handlers (for compatibility)
                    if (typeof value === 'function') {
                        element.addEventListener(key.substring(2).toLowerCase(), value);
                    }
                } else if (key === 'value' && (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA')) {
                    // Handle form input values as properties, not attributes
                    element.value = value;
                } else if (key === 'checked' && element.tagName === 'INPUT') {
                    // Handle checkbox/radio checked state as property
                    element.checked = value;
                } else {
                    // Handle other attributes
                    element.setAttribute(key, value);
                }
            }
        }

        // Process children recursively, passing eventContext down
        if (node.children && Array.isArray(node.children)) {
            node.children.forEach(child => {
                const childElement = createDOMElement(child, eventContext);
                if (childElement) {
                    element.appendChild(childElement);
                }
            });
        }

        return element;
    }

    // Return null or undefined for invalid nodes
    console.warn('Invalid virtual DOM node:', node);
    return null;
}