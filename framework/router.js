// framework/router.js

/**
 * Simple routing system that synchronizes app state with URL
 * Provides navigation methods and route handling
 */
export class Router {
    constructor() {
        this.routes = new Map();
        this.currentRoute = null;
        this.onRouteChange = null;

        // Listen for popstate events (browser back/forward)
        window.addEventListener('popstate', () => {
            this.handleRoute();
        });

        // Intercept link clicks for SPA navigation
        document.addEventListener('click', (event) => {
            const link = event.target.closest('a');
            if (link && link.href) {
                try {
                    const url = new URL(link.href);
                    if (url.origin === window.location.origin) {
                        event.preventDefault();
                        this.navigate(url.pathname);
                    }
                } catch (e) {
                    // Let browser handle invalid URLs
                }
            }
        });
    }

    /**
     * Register a route with a handler function
     * @param {string} path - The URL path (e.g., '/', '/active')
     * @param {Function} handler - Function to call when route matches
     */
    addRoute(path, handler) {
        this.routes.set(path, handler);
    }

    /**
     * Set callback for route changes
     * @param {Function} callback - Function to call on route change
     */
    onRoute(callback) {
        this.onRouteChange = callback;
    }

    /**
     * Navigate to a specific path
     * @param {string} path - The URL path to navigate to
     */
    navigate(path) {
        if (this.currentRoute !== path) {
            history.pushState(null, '', path);
            this.handleRoute();
        }
    }

    /**
     * Get the current route path
     * @returns {string} Current path
     */
    getCurrentPath() {
        return window.location.pathname;
    }

    /**
     * Handle route changes and call appropriate handlers
     */
    handleRoute() {
        const path = this.getCurrentPath();
        const handler = this.routes.get(path);
        
        if (handler) {
            this.currentRoute = path;
            handler(path);
        } else {
            // Try to find a default route or handle 404
            const defaultHandler = this.routes.get('*') || this.routes.get('/');
            if (defaultHandler) {
                this.currentRoute = path;
                defaultHandler(path);
            }
        }

        // Call the route change callback if set
        if (this.onRouteChange) {
            this.onRouteChange(path);
        }
    }

    /**
     * Initialize the router and handle the current route
     */
    init() {
        this.handleRoute();
    }
}
